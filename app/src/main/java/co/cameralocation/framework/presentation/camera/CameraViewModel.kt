package co.cameralocation.framework.presentation.camera

import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.media.MediaMetadataRetriever
import android.net.Uri
import android.os.CountDownTimer
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.util.Log
import androidx.lifecycle.viewModelScope
import com.otaliastudios.cameraview.controls.Facing
import com.otaliastudios.cameraview.controls.Flash
import com.otaliastudios.cameraview.controls.Mode
import com.otaliastudios.cameraview.size.AspectRatio
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import co.cameralocation.framework.presentation.common.BaseViewModel
import co.cameralocation.framework.presentation.common.launchIO
import co.cameralocation.framework.presentation.model.camera.CameraGridMode
import co.cameralocation.framework.presentation.model.camera.CameraUiState
import co.cameralocation.framework.presentation.model.locationinfo.LocationInfo
import co.cameralocation.framework.presentation.model.media.CameraGPSSavedMediaType
import co.cameralocation.framework.repository.FFmpegRepository
import co.cameralocation.framework.repository.FileRepository
import co.cameralocation.framework.repository.MediaRepository
import co.cameralocation.framework.repository.implementation.FileRepositoryImpl
import co.cameralocation.framework.usecase.GetCurrentLocationUseCase
import co.cameralocation.framework.presentation.model.camera.LocationViewRenderingPosition
import android.graphics.Bitmap
import timber.log.Timber
import java.io.File
import javax.inject.Inject
import co.cameralocation.framework.repository.Result as RepositoryResult

@HiltViewModel
class CameraViewModel @Inject constructor(
    private val fileRepository: FileRepository,
    private val ffmpegRepository: FFmpegRepository,
    private val mediaRepository: MediaRepository,
    private val getCurrentLocationUseCase: GetCurrentLocationUseCase,
) : BaseViewModel() {

    private val _uiState = MutableStateFlow(CameraUiState())
    val uiState: StateFlow<CameraUiState> = _uiState.asStateFlow()

    private val _listPartVideoFiles = MutableStateFlow<List<File>>(emptyList())
    val listPartVideoFiles: StateFlow<List<File>> = _listPartVideoFiles.asStateFlow()

    private var countDownTimer: CountDownTimer? = null
    private var recordingTimer: Handler? = null
    private var recordingRunnable: Runnable? = null

    fun addPartVideoFile(file: File) {
        Log.d("CameraViewModel", "addPartVideoFile: ${file.absolutePath}")
        _listPartVideoFiles.update { currentState ->
            currentState + file
        }

        Log.d("CameraViewModel", "addPartVideoFile: ${listPartVideoFiles.value.size}")
    }

    fun setTakingPicture(isTakingPicture: Boolean) {
        _uiState.update { currentState ->
            currentState.copy(
                isTakingPicture = isTakingPicture
            )
        }
    }

    fun setTakingVideo(isTakingVideo: Boolean) {
        _uiState.update { currentState ->
            currentState.copy(
                isTakingVideo = isTakingVideo
            )
        }
    }

    fun toggleFlash() {
        _uiState.update { currentState ->
            val newFlashMode = when (currentState.cameraSettingUiState.flashMode) {
                Flash.TORCH -> Flash.OFF
                else -> Flash.TORCH
            }
            currentState.copy(
                cameraSettingUiState = currentState.cameraSettingUiState.copy(flashMode = newFlashMode)
            )
        }
    }

    fun toggleCameraFacing() {
        _uiState.update { currentState ->
            val newFacing = when (currentState.cameraSettingUiState.cameraFacing) {
                Facing.BACK -> Facing.FRONT
                else -> Facing.BACK
            }
            currentState.copy(
                cameraSettingUiState = currentState.cameraSettingUiState.copy(
                    cameraFacing = newFacing
                )
            )
        }
    }

    fun setGridMode(gridMode: CameraGridMode) {
        _uiState.update { currentState ->
            currentState.copy(
                cameraSettingUiState = currentState.cameraSettingUiState.copy(
                    gridMode = gridMode
                )
            )
        }
    }

    fun setAspectRatio(aspectRatio: AspectRatio) {
        _uiState.update { currentState ->
            currentState.copy(
                cameraSettingUiState = currentState.cameraSettingUiState.copy(
                    aspectRatio = aspectRatio
                )
            )
        }
    }

    fun setDeviceOrientation(orientation: Int) {
        _uiState.update { currentState ->
            currentState.copy(
                cameraSettingUiState = currentState.cameraSettingUiState.copy(
                    deviceOrientation = orientation
                )
            )
        }
    }

    fun getCurrentLocation(
        onSuccess: (LocationInfo) -> Unit,
        onError: (Exception?) -> Unit
    ) {
        launchIO {
            getCurrentLocationUseCase(
                onSuccess = { locationInfo ->
                    onSuccess(locationInfo)
                },
                onError = onError
            )
        }
    }

    fun startVideoRecording() {
        _uiState.update { currentState ->
            currentState.copy(
                cameraVideoRecorderUiState = currentState.cameraVideoRecorderUiState.copy(
                    isRecordingSessionRunning = true,
                ),
                cameraSettingUiState = currentState.cameraSettingUiState.copy(
                    cameraMode = Mode.VIDEO
                )
            )
        }
    }

    fun stopVideoRecording() {
        _uiState.update { currentState ->
            currentState.copy(
                cameraVideoRecorderUiState = currentState.cameraVideoRecorderUiState.copy(
                    isRecordingSessionRunning = false,
                ),
                cameraSettingUiState = currentState.cameraSettingUiState.copy(
                    cameraMode = Mode.PICTURE
                )
            )
        }
    }

    fun saveImageToDCIM(
        currentLocation: LocationInfo?,
        bitmap: Bitmap?,
        savePath: String,
        onSuccess: (uri: Uri?) -> Unit,
        onError: () -> Unit
    ) {
        startSaving()
        launchIO {
            if (bitmap == null) {
                stopSaving()
                onError()
                return@launchIO
            }

            fileRepository.saveImageToDCIM(
                bitmap,
                savePath,
                onSuccess = { uri ->
                    // Create a MediaItem entry for the saved photo
                    try {
                        val mediaItem = mediaRepository.createMediaItem(
                            uri = uri,
                            path = uri.path ?: "",
                            type = CameraGPSSavedMediaType.PHOTO,
                            locationId = currentLocation?.id,
                            locationName = currentLocation?.name,
                            latitude = currentLocation?.latitude,
                            longitude = currentLocation?.longitude
                        )
                        Timber.tag("CameraViewModel").d("Created MediaItem for photo: $mediaItem")
                    } catch (e: Exception) {
                        Timber.tag("CameraViewModel").e(e, "Error creating MediaItem for photo")
                    }

                    stopSaving()
                    onSuccess(uri)
                },
                onError = {
                    stopSaving()
                    onError()
                }
            )
        }
    }

    fun loadImageFromDCIM(
        maxImages: Int,
        onSuccess: (uri: Uri?) -> Unit,
        onError: () -> Unit
    ) {
        launchIO {
            val imageFiles = fileRepository.readImagesFromSpecificDCIMPath(
                FileRepositoryImpl.DEFAULT_CAMERA_GPS_FOLDER_PATH,
                maxImages
            )
            if (imageFiles.isNotEmpty()) {
                onSuccess(imageFiles.firstOrNull()?.imageUri)
            } else {
                onError()
            }
        }
    }

    fun openCameraSettings(context: Context) {
        val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
        val uri = Uri.fromParts("package", context.packageName, null)
        intent.data = uri
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        context.startActivity(intent)
    }

    fun setTimerSeconds(seconds: Int) {
        _uiState.update { currentState ->
            currentState.copy(
                cameraPictureCountdownUiState = currentState.cameraPictureCountdownUiState.copy(
                    timerEnabled = seconds > 0,
                    timerSeconds = seconds,
                    timerCountdown = seconds
                )
            )
        }
    }

    fun startTimer(onFinished: () -> Unit) {
        cancelTimer()

        _uiState.update { currentState ->
            currentState.copy(
                cameraPictureCountdownUiState = currentState.cameraPictureCountdownUiState.copy(
                    isTimerRunning = true,
                    timerCountdown = currentState.cameraPictureCountdownUiState.timerSeconds
                )
            )
        }

        countDownTimer = object :
            CountDownTimer(uiState.value.cameraPictureCountdownUiState.timerSeconds * 1000L, 1000) {
            override fun onTick(millisUntilFinished: Long) {
                val secondsRemaining = (millisUntilFinished / 1000).toInt()
                _uiState.update { currentState ->
                    currentState.copy(
                        cameraPictureCountdownUiState = currentState.cameraPictureCountdownUiState.copy(
                            timerCountdown = secondsRemaining
                        )
                    )
                }
            }

            override fun onFinish() {
                _uiState.update { currentState ->
                    currentState.copy(
                        cameraPictureCountdownUiState = currentState.cameraPictureCountdownUiState.copy(
                            isTimerRunning = false,
                            timerCountdown = 0
                        )
                    )
                }
                onFinished()
            }
        }.start()
    }

    fun cancelTimer() {
        countDownTimer?.cancel()
        countDownTimer = null
        _uiState.update { currentState ->
            currentState.copy(
                cameraPictureCountdownUiState = currentState.cameraPictureCountdownUiState.copy(
                    isTimerRunning = false
                )
            )
        }
    }

    fun startRecordingTimer() {
        stopRecordingTimer()

        recordingTimer = Handler(Looper.getMainLooper())
        recordingRunnable = object : Runnable {
            override fun run() {
                if (uiState.value.cameraVideoRecorderUiState.isRecordingSessionRunning && !uiState.value.cameraVideoRecorderUiState.isRecordingPaused) {
                    _uiState.update { currentState ->
                        currentState.copy(
                            cameraVideoRecorderUiState = currentState.cameraVideoRecorderUiState.copy(
                                recordingTimeSeconds = currentState.cameraVideoRecorderUiState.recordingTimeSeconds + 1
                            )
                        )
                    }
                }
                recordingTimer?.postDelayed(this, 1000)
            }
        }
        recordingRunnable?.let { recordingTimer?.post(it) }
    }

    fun stopRecordingTimer() {
        recordingRunnable?.let { recordingTimer?.removeCallbacks(it) }
        recordingTimer = null
        recordingRunnable = null
    }

    fun pauseVideoRecording() {
        _uiState.update { currentState ->
            currentState.copy(
                cameraVideoRecorderUiState = currentState.cameraVideoRecorderUiState.copy(
                    isRecordingPaused = true
                )
            )
        }
    }

    fun resumeVideoRecording() {
        _uiState.update { currentState ->
            currentState.copy(
                cameraVideoRecorderUiState = currentState.cameraVideoRecorderUiState.copy(
                    isRecordingPaused = false
                )
            )
        }
    }

    fun getPartVideoOutputFile(context: Context, partIndex: Int): File {
        return File(
            context.cacheDir,
            "Video_CameraGPS_${System.currentTimeMillis()}_Part${partIndex}.mp4"
        )
    }

    fun getFullVideoOutputFile(context: Context): File {
        return File(context.cacheDir, "Video_CameraGPS_${System.currentTimeMillis()}.mp4")
    }

    fun mergeVideoFilesThenSaveCacheDir(
        context: Context,
        onSuccess: (Uri) -> Unit,
        onError: () -> Unit
    ) {
        Timber.d("mergeVideoFilesThenSaveCacheDir: locationBitmapFile starting...")
        startSaving()
        launchIO {
            // If we have a location bitmap file, use FFmpeg to overlay it on the video
            val outputFile = getFullVideoOutputFile(context)
            ffmpegRepository.concatVideos(
                listPartVideoFiles.value.map { it.absolutePath },
                outputFile.absolutePath
            ).collect { result ->
                handleVideoProcessingResult(
                    result = result,
                    outputFile = outputFile,
                    onSuccess = onSuccess,
                    onError = onError
                )
            }
        }
    }

    private suspend fun handleVideoProcessingResult(
        result: RepositoryResult<File>,
        outputFile: File,
        onSuccess: (Uri) -> Unit,
        onError: () -> Unit
    ) = withContext(Dispatchers.IO) {
        val onSuccessEvent: (Uri) -> Unit = {
            stopSaving()
            onSuccess(it)
        }

        val onErrorEvent: () -> Unit = {
            stopSaving()
            onError()
        }

        when (result) {
            is RepositoryResult.Success -> {
                fileRepository.saveVideoToCacheDir(
                    outputFile,
                    FileRepositoryImpl.DEFAULT_CAMERA_GPS_FOLDER_PATH,
                    onSuccess = { uri ->
                        Timber.tag("CameraViewModel")
                            .d("mergeVideoFilesThenSave: saveVideoToDCIM success=$uri")
                        onSuccessEvent(uri)
                    },
                    onError = {
                        Timber.tag("CameraViewModel")
                            .d("mergeVideoFilesThenSave: saveVideoToDCIM error")
                        onErrorEvent()
                    })
            }

            is RepositoryResult.Error -> {
                Timber.tag("CameraViewModel")
                    .d("mergeVideoFilesThenSave: error=${result.error.message}")
                onErrorEvent()
            }
        }
    }

    fun saveVideoToDCIM(
        currentLocation: LocationInfo?,
        sourceFile: File,
        onSuccess: (Uri) -> Unit,
        onError: () -> Unit
    ) {
        launchIO {
            try {
                fileRepository.saveVideoToDCIM(
                    sourceFile,
                    FileRepositoryImpl.DEFAULT_CAMERA_GPS_FOLDER_PATH,
                    onSuccess = { uri ->
                        Timber.tag("CameraViewModel")
                            .d("saveVideoToDCIM: saveVideoToDCIM success=$uri")

                        // Create a MediaItem entry for the saved video
                        try {
                            // Get video duration
                            var duration: Long? = null
                            try {
                                val retriever = MediaMetadataRetriever()
                                retriever.setDataSource(sourceFile.absolutePath)
                                val durationStr = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION)
                                duration = durationStr?.toLongOrNull()
                                retriever.release()
                            } catch (e: Exception) {
                                Timber.e(e, "Error getting video duration")
                            }

                            val mediaItem = mediaRepository.createMediaItem(
                                uri = uri,
                                path = uri.path ?: "",
                                type = CameraGPSSavedMediaType.VIDEO,
                                locationId = currentLocation?.id,
                                locationName = currentLocation?.name,
                                latitude = currentLocation?.latitude,
                                longitude = currentLocation?.longitude,
                                duration = duration
                            )
                            Timber.d("Created MediaItem for video: $mediaItem")
                        } catch (e: Exception) {
                            Timber.e(e, "Error creating MediaItem for video")
                        }

                        onSuccess(uri)
                    },
                    onError = {
                        Timber.tag("CameraViewModel")
                            .d("saveVideoToDCIM: saveVideoToDCIM error")
                        onError()
                    }
                )
            } catch (e: Exception) {
                Log.e("CameraViewModel", "Error saving video to DCIM: ${e.message}")
                onError()
            }
        }
    }

    fun clearPartsVideoFiles() {
        _listPartVideoFiles.update { emptyList() }
    }

    fun startSaving() {
        _uiState.update { currentState ->
            currentState.copy(isSaving = true)
        }
    }

    fun stopSaving() {
        _uiState.update { currentState ->
            currentState.copy(isSaving = false)
        }
    }

    fun saveVideoToCacheDir(
        sourceFile: File,
        onSuccess: (File) -> Unit,
        onError: () -> Unit
    ) {
        viewModelScope.launch {
            try {
                fileRepository.saveVideoToCacheDir(
                    sourceFile,
                    FileRepositoryImpl.DEFAULT_CAMERA_GPS_FOLDER_PATH,
                    onSuccess = { uri ->
                        Timber.tag("CameraViewModel")
                            .d("saveVideoToCacheDir: saveVideoToDCIM success=$uri")
                    },
                    onError = {
                        Timber.tag("CameraViewModel")
                            .d("saveVideoToCacheDir: saveVideoToDCIM error")
                    }
                )

                onSuccess(sourceFile)
            } catch (e: Exception) {
                Log.e("CameraViewModel", "Error saving video to cache: ${e.message}")
                onError()
            }
        }
    }

    fun setCameraMode(mode: Mode) {
        _uiState.update { currentState ->
            currentState.copy(
                cameraSettingUiState = currentState.cameraSettingUiState.copy(
                    cameraMode = mode
                ),
                cameraVideoRecorderUiState = currentState.cameraVideoRecorderUiState.copy(
                    isRecordingSessionRunning = false,
                    isRecordingPaused = false,
                    recordingTimeSeconds = 0
                )
            )
        }
        stopRecordingTimer()
    }
}
