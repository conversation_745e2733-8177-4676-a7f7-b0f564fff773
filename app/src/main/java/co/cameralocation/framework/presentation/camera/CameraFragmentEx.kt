package co.cameralocation.framework.presentation.camera

import android.graphics.Bitmap
import android.graphics.PointF
import android.util.Log
import android.view.Gravity
import android.view.View
import android.widget.FrameLayout
import androidx.appcompat.app.AlertDialog
import androidx.core.graphics.scale
import androidx.core.view.isVisible
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.flowWithLifecycle
import androidx.lifecycle.lifecycleScope
import com.otaliastudios.cameraview.CameraListener
import com.otaliastudios.cameraview.CameraOptions
import com.otaliastudios.cameraview.PictureResult
import com.otaliastudios.cameraview.VideoResult
import com.otaliastudios.cameraview.controls.Audio
import com.otaliastudios.cameraview.controls.Engine
import com.otaliastudios.cameraview.controls.Mode
import com.otaliastudios.cameraview.gesture.Gesture
import com.otaliastudios.cameraview.gesture.GestureAction
import com.otaliastudios.cameraview.overlay.OverlayLayout
import com.otaliastudios.cameraview.size.AspectRatio
import com.otaliastudios.cameraview.size.SizeSelectors
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import co.cameralocation.R
import co.cameralocation.customview.locationinfo.LocationInfoLayoutFactory
import co.cameralocation.framework.presentation.camera.CameraFragment.Companion.TAG
import co.cameralocation.framework.presentation.common.launchMain
import co.cameralocation.framework.presentation.editlocation.adapter.isTitleCompletelyVisible
import co.cameralocation.framework.presentation.model.camera.CameraGridMode
import co.cameralocation.framework.presentation.model.camera.isPictureCountdownTimerCompletelyVisible
import co.cameralocation.framework.presentation.model.camera.isRecordingTimerCompletelyVisible
import co.cameralocation.framework.presentation.model.camera.isShowingEditLocationButton
import co.cameralocation.framework.presentation.model.camera.isShowingGoToSaveMediaButton
import co.cameralocation.framework.presentation.model.camera.isShowingPauseResumeButton
import co.cameralocation.framework.presentation.model.camera.isShowingSwitchFacingButton
import co.cameralocation.framework.presentation.model.camera.isShowingToggleBetweenPictureAndVideoModeButton
import co.cameralocation.framework.presentation.model.camera.isTimeButtonCompletelyVisible
import co.cameralocation.framework.repository.implementation.FileRepositoryImpl
import co.cameralocation.util.BitmapUtils
import co.cameralocation.util.TimeUtils
import co.cameralocation.util.collectFlowOnView
import co.cameralocation.util.displayToast
import co.cameralocation.util.isAllPermissionGranted
import co.cameralocation.util.removeSelf
import co.cameralocation.util.requestPermission
import co.cameralocation.util.setPreventDoubleClick
import co.cameralocation.framework.presentation.camera.renderer.LocationViewRenderer
import co.cameralocation.framework.presentation.model.camera.LocationViewRenderingPosition
import com.otaliastudios.cameraview.preview.GlCameraPreview
import timber.log.Timber
import java.io.File

fun CameraFragment.backEvent() {
    Log.d(TAG, "backEvent: ")
    activity?.onBackPressedDispatcher?.onBackPressed()
}

fun CameraFragment.setupBackButton() {
    binding.btnBack.setPreventDoubleClick {
        backEvent()
    }
}

fun CameraFragment.setUpCameraView() {
    cameraView = binding.flCameraView

    binding.root.post {
        cameraView?.mode = Mode.PICTURE
        cameraView?.audio = Audio.OFF
        cameraView?.engine = Engine.CAMERA2
        cameraView?.playSounds = false
        cameraView?.setLifecycleOwner(viewLifecycleOwner)

        cameraView?.mapGesture(Gesture.PINCH, GestureAction.ZOOM)
        cameraView?.mapGesture(Gesture.TAP, GestureAction.AUTO_FOCUS)

        cameraView?.pictureSnapshotMetering = true

        // Initialize OpenGL LocationView Renderer
        setupLocationViewRenderer()

        cameraView?.addCameraListener(object : CameraListener() {
            override fun onCameraOpened(options: CameraOptions) {
                super.onCameraOpened(options)
            }

            override fun onZoomChanged(
                newValue: Float,
                bounds: FloatArray,
                fingers: Array<out PointF>?
            ) {
                super.onZoomChanged(newValue, bounds, fingers)
            }

            override fun onExposureCorrectionChanged(
                newValue: Float,
                bounds: FloatArray,
                fingers: Array<out PointF>?
            ) {
                super.onExposureCorrectionChanged(newValue, bounds, fingers)
            }

            override fun onAutoFocusStart(point: PointF) {
                super.onAutoFocusStart(point)
            }

            override fun onAutoFocusEnd(successful: Boolean, point: PointF) {
                super.onAutoFocusEnd(successful, point)
            }

            override fun onPictureTaken(result: PictureResult) {
                super.onPictureTaken(result)

                // Convert picture to bitmap
                result.toBitmap { originalBitmap ->
                    if (originalBitmap == null) return@toBitmap

                    // OpenGL rendering handles overlay during capture, so use original bitmap directly
                    val finalBitmap = originalBitmap

                    viewModel.setTakingPicture(false)

                    // Show result dialog with the composite bitmap
                    CameraResultDialogFragment.Builder()
                        .setType(CameraResultDialogFragmentType.Picture(finalBitmap))
                        .setOnAcceptListener {
                            saveCurrentPicture(finalBitmap)
                        }
                        .setOnCancelListener {
                            // Do nothing
                        }
                        .build()
                        .show(childFragmentManager, TAG)
                }
            }

            override fun onVideoRecordingStart() {
                super.onVideoRecordingStart()
                Log.d("CameraFragment", "Video recording started")
                // Update UI for recording state
                //binding.btnVideo.setImageResource(R.drawable.ic_stop)
            }

            override fun onVideoRecordingEnd() {
                super.onVideoRecordingEnd()
                Log.d("CameraFragment", "Video recording ended")
                // Reset UI after recording
                //binding.btnVideo.setImageResource(R.drawable.ic_video)

            }

            override fun onVideoTaken(result: VideoResult) {
                super.onVideoTaken(result)
                // Save to cache directory first
                viewModel.saveVideoToCacheDir(
                    sourceFile = result.file,
                    onSuccess = { cachedFile ->
                        // Add the cached file to parts list
                        viewModel.addPartVideoFile(cachedFile)

                        if (isRequestSaveFullVideoFileAfterSavingPartDone) {
                            isRequestSaveFullVideoFileAfterSavingPartDone = false
                            onSaveVideoEvent()
                        }
                    },
                    onError = {
                        //Do nothing
                    }
                )
            }

            override fun onOrientationChanged(orientation: Int) {
                super.onOrientationChanged(orientation)
                viewModel.setDeviceOrientation(orientation)
            }
        })
    }
}


fun CameraFragment.onSaveVideoEvent() {
    val notifyClearData = {
        partIndex.set(0)
        viewModel.clearPartsVideoFiles()
        viewModel.setTakingVideo(false)
    }

    Timber.d("onSaveVideoEvent: mergeVideoFilesThenSaveCacheDir: starting...")
    viewModel.mergeVideoFilesThenSaveCacheDir(
        context = requireContext(),
        onSuccess = { uri ->
            launchMain {
                Timber.d("mergeVideoFilesThenSaveCacheDir: successfully created full video file at URI: $uri")
                CameraResultDialogFragment.Builder()
                    .setType(CameraResultDialogFragmentType.Video(uri))
                    .setOnAcceptListener {
                        viewModel.saveVideoToDCIM(
                            currentLocation = commonViewModel.commonUiState.value.defaultLocationInfo,
                            sourceFile = File(uri.path),
                            onSuccess = {
                                launchMain {
                                    displayToast(getString(R.string.video_saved_format, it))
                                }
                            },
                            onError = {
                                launchMain {
                                    displayToast(getString(R.string.error_saving_video))
                                }
                            }
                        )
                    }
                    .setOnCancelListener {
                        // Do nothing
                    }
                    .build()
                    .show(childFragmentManager, TAG)
            }
            notifyClearData()
        },
        onError = {
            notifyClearData()
        }
    )
}

fun CameraFragment.setupGridButton() {
    binding.btnGrid.setPreventDoubleClick {
        val builder = AlertDialog.Builder(requireContext())
        builder.setTitle(getString(R.string.select_grid))
        builder.setItems(CameraGridMode.entries.map { getString(it.label) }.toTypedArray()) { dialog, position ->
            viewModel.setGridMode(CameraGridMode.entries[position])
            prefUtil.defaultCameraGridMode = CameraGridMode.entries[position]
            dialog.dismiss()
        }
        builder.show()
    }
}

fun CameraFragment.setupCaptureButton() {
    binding.btnCapture.setPreventDoubleClick {
        val uiState = viewModel.uiState.value
        when (uiState.cameraSettingUiState.cameraMode) {
            Mode.VIDEO -> {
                if (uiState.cameraVideoRecorderUiState.isRecordingSessionRunning) {
                    // Stop recording
                    viewModel.stopVideoRecording()
                    viewModel.stopRecordingTimer()
                    cameraView?.stopVideo()

                    if (!uiState.cameraVideoRecorderUiState.isRecordingPaused) {
                        isRequestSaveFullVideoFileAfterSavingPartDone = true
                    } else {
                        onSaveVideoEvent()
                    }
                } else {
                    // Start recording
                    val file = viewModel.getPartVideoOutputFile(
                        requireContext(),
                        partIndex.incrementAndGet()
                    )
                    viewModel.setTakingVideo(true)
                    cameraView?.takeVideoSnapshot(file)
                    viewModel.startVideoRecording()
                    viewModel.startRecordingTimer()
                }
            }

            Mode.PICTURE -> {
                if (uiState.cameraPictureCountdownUiState.timerEnabled && !uiState.cameraPictureCountdownUiState.isTimerRunning) {
                    // Start timer and take picture when timer finishes
                    viewModel.startTimer {
                        viewModel.setTakingPicture(true)
                        cameraView?.takePicture()
                    }
                } else if (!uiState.cameraPictureCountdownUiState.isTimerRunning) {
                    // Take picture immediately
                    viewModel.setTakingPicture(true)
                    cameraView?.takePicture()
                }
            }
        }
    }
}

fun CameraFragment.setupSwitchCameraButton() {
    binding.btnSwitchCamera.setPreventDoubleClick {
        viewModel.toggleCameraFacing()
    }
}

fun CameraFragment.setupFlashButton() {
    binding.btnFlash.setPreventDoubleClick {
        viewModel.toggleFlash()
    }
}

fun CameraFragment.setupRatioSpinner() {
    val ratios = listOf(
        getString(R.string.ratio_1_1),
        getString(R.string.ratio_3_2),
        getString(R.string.ratio_4_3),
        getString(R.string.ratio_16_9),
        getString(R.string.ratio_18_9)
    )

    binding.btnRatio.setPreventDoubleClick {
        val builder = AlertDialog.Builder(requireContext())
        builder.setTitle(getString(R.string.select_aspect_ratio))
        builder.setItems(ratios.toTypedArray()) { dialog, position ->
            val ratioStr = ratios[position]
            val aspectRatio = AspectRatio.parse(ratioStr)
            viewModel.setAspectRatio(aspectRatio)
            prefUtil.defaultCameraAspectRatio = aspectRatio.toString()
            // Show toast with selected ratio
            displayToast(getString(R.string.selected_ratio_format, ratioStr))
            dialog.dismiss()
        }

        builder.show()
    }
}

fun CameraFragment.observeCameraFacing() {
    lifecycleScope.launch {
        viewModel.uiState
            .map { it.cameraSettingUiState.cameraFacing }
            .distinctUntilChanged()
            .collectLatest { facing ->
                if (cameraView?.facing != facing) {
                    cameraView?.facing = facing
                }
            }
    }
}

fun CameraFragment.observeFlashMode() {
    lifecycleScope.launch {
        viewModel.uiState
            .map { it.cameraSettingUiState.flashMode }
            .distinctUntilChanged()
            .collectLatest { flashMode ->
                if (cameraView?.flash != flashMode) {
                    cameraView?.flash = flashMode
                }
            }
    }
}

fun CameraFragment.observeGridMode() {
    lifecycleScope.launch {
        viewModel.uiState
            .map { it.cameraSettingUiState.gridMode }
            .distinctUntilChanged()
            .collectLatest {
                cameraView?.grid = it.gridMode
                binding.btnGrid.setImageBitmap(gridIcons[it])
            }
    }
}

fun CameraFragment.observeAspectRatio() {
    lifecycleScope.launch {
        viewModel.uiState
            .map { it.cameraSettingUiState.aspectRatio }
            .distinctUntilChanged()
            .collectLatest { aspectRatio ->
                Log.d("CameraFragment", "Aspect ratio changed: $aspectRatio")
                if (cameraView != null) {
                    try {
                        val sizeSelectors = SizeSelectors.aspectRatio(aspectRatio, 0.1f)
                        cameraView?.setPictureSize(sizeSelectors)
                        cameraView?.setVideoSize(sizeSelectors)
                        cameraView?.setPreviewStreamSize(sizeSelectors)

                        //Restart camera to apply size
                        cameraView?.close()
                        cameraView?.open()
                    } catch (e: Exception) {
                        Log.e("CameraFragment", "Error setting aspect ratio: ${e.message}")
                    }

                    when(aspectRatio) {
                        AspectRatio.of(1, 1) -> binding.btnRatio.setImageResource(R.drawable.ic_ratio_1_1_camera_screen)
                        AspectRatio.of(3, 2) -> binding.btnRatio.setImageResource(R.drawable.ic_ratio_3_2_camera_screen)
                        AspectRatio.of(4, 3) -> binding.btnRatio.setImageResource(R.drawable.ic_ratio_4_3_camera_screen)
                        AspectRatio.of(16, 9) -> binding.btnRatio.setImageResource(R.drawable.ic_ratio_16_9_camera_screen)
                        AspectRatio.of(18, 9) -> binding.btnRatio.setImageResource(R.drawable.ic_ratio_18_9_camera_screen)
                    }
                }
            }
    }
}

fun CameraFragment.observeCameraMode() {
    lifecycleScope.launch {
        viewModel.uiState
            .map { it.cameraSettingUiState.cameraMode }
            .distinctUntilChanged()
            .collectLatest { cameraMode ->
                if (cameraView?.mode != cameraMode) {
                    cameraView?.mode = cameraMode
                }

                // Update UI based on camera mode
                updateCameraModeUI(cameraMode)
            }
    }
}

fun CameraFragment.updateCameraModeUI(mode: Mode) {
    when (mode) {
        Mode.PICTURE -> {
            binding.tabPhoto.isSelected = true
            binding.tabVideo.isSelected = false
            binding.btnCapture.setImageResource(R.drawable.ic_capture_camera_screen)
        }

        Mode.VIDEO -> {
            binding.tabVideo.isSelected = true
            binding.tabPhoto.isSelected = false
            if (viewModel.uiState.value.cameraVideoRecorderUiState.isRecordingSessionRunning) {
                binding.btnCapture.setImageResource(R.drawable.ic_stop_recording_video_camera_screen)
            } else {
                binding.btnCapture.setImageResource(R.drawable.ic_capture_video_mode_camera_screen)
            }
        }
    }
}

fun CameraFragment.setupSwitchModeButton() {
    binding.tabPhoto.setOnClickListener {
        if (viewModel.uiState.value.cameraSettingUiState.cameraMode != Mode.PICTURE) {
            viewModel.setCameraMode(Mode.PICTURE)
        }
    }

    binding.tabVideo.setOnClickListener {
        if (viewModel.uiState.value.cameraSettingUiState.cameraMode != Mode.VIDEO) {
            viewModel.setCameraMode(Mode.VIDEO)
        }
    }
}

fun CameraFragment.setupSettingsButton() {
    binding.btnSettings.setPreventDoubleClick {
        // Open system camera settings
        viewModel.openCameraSettings(requireContext())
    }
}

fun CameraFragment.setupTimerButton() {
    binding.btnTime.setPreventDoubleClick {
        if (viewModel.uiState.value.cameraSettingUiState.cameraMode == Mode.PICTURE) {
            val timerOptions = arrayOf(
                getString(R.string.off),
                getString(R.string.timer_seconds_countdown, 3),
                getString(R.string.timer_seconds_countdown, 5),
                getString(R.string.timer_seconds_countdown, 10)
            )

            val timerValues = arrayOf(0, 3, 5, 10)

            val builder = AlertDialog.Builder(requireContext())
            builder.setTitle(getString(R.string.select_timer))
            builder.setItems(timerOptions) { dialog, position ->
                viewModel.setTimerSeconds(timerValues[position])
                prefUtil.defaultCameraPictureCountdownTimerSeconds = timerValues[position]
                dialog.dismiss()
            }
            builder.show()
        }
    }
}

fun CameraFragment.setupButtonSaveMediaEvent() {
    binding.imgSavePhotoContent.setPreventDoubleClick {
        safeNav(
            R.id.cameraFragment,
            R.id.action_cameraFragment_to_savePhotoFragment
        )
    }
}

fun CameraFragment.setupPauseResumeButton() {
    binding.btnPauseOrResumeVideoRecording.setPreventDoubleClick {
        val uiState = viewModel.uiState.value
        if (uiState.cameraVideoRecorderUiState.isRecordingSessionRunning) {
            if (uiState.cameraVideoRecorderUiState.isRecordingPaused) {
                // Resume recording
                viewModel.resumeVideoRecording()
                val file =
                    viewModel.getPartVideoOutputFile(requireContext(), partIndex.incrementAndGet())
                viewModel.setTakingVideo(true)
                cameraView?.takeVideoSnapshot(file)
                binding.btnPauseOrResumeVideoRecording.setImageResource(R.drawable.ic_pause_recording_camera_screen) // Replace with your pause icon
            } else {
                // Pause recording
                viewModel.pauseVideoRecording()
                cameraView?.stopVideo()
                binding.btnPauseOrResumeVideoRecording.setImageResource(R.drawable.ic_resume_recording_camera_screen) // Replace with your resume icon
            }
        }
    }
}

@OptIn(ExperimentalCoroutinesApi::class)
fun CameraFragment.observeRecordingState() {
    lifecycleScope.launch {
        viewModel.uiState
            .flatMapLatest {
                if (it.cameraSettingUiState.cameraMode == Mode.VIDEO) {
                    flowOf(
                        Triple(
                            it.cameraVideoRecorderUiState.isRecordingSessionRunning,
                            it.cameraVideoRecorderUiState.isRecordingPaused,
                            it.cameraVideoRecorderUiState.recordingTimeSeconds
                        )
                    )
                } else {
                    flowOf(null)
                }
            }
            .filterNotNull()
            .distinctUntilChanged()
            .collectLatest { (isRecording, isPaused, timeSeconds) ->
                // Update recording timer display
                val hours = timeSeconds / 3600
                val minutes = (timeSeconds % 3600) / 60
                val seconds = timeSeconds % 60
                binding.tvRecordingTime.text = buildString {
                    append(if (isPaused) getString(R.string.paused) else "")
                    append(" ")
                    append(String.format("%02d:%02d:%02d", hours, minutes, seconds))
                }
                // Update UI based on recording state
                if (isRecording) {
                    if (isPaused) {
                        binding.btnPauseOrResumeVideoRecording.setImageResource(R.drawable.ic_resume_recording_camera_screen)
                    } else {
                        binding.btnPauseOrResumeVideoRecording.setImageResource(R.drawable.ic_pause_recording_camera_screen)
                    }
                }

                updateCameraModeUI(Mode.VIDEO)
            }
    }
}

fun CameraFragment.observeTimerState() {
    lifecycleScope.launch {
        viewModel.uiState
            .map {
                Triple(
                    it.cameraPictureCountdownUiState.isTimerRunning,
                    it.cameraPictureCountdownUiState.timerEnabled,
                    it.cameraPictureCountdownUiState.timerCountdown
                )
            }
            .distinctUntilChanged()
            .collectLatest { (isRunning, isEnabled, countdown) ->
                if (isRunning) {
                    val seconds = countdown % 60
                    binding.tvCountdownTimerPicture.text = seconds.toString()
                } else {
                    binding.tvCountdownTimerPicture.text = ""
                }
            }
    }
}

fun CameraFragment.saveCurrentPicture(bitmap: Bitmap) {
    viewModel.saveImageToDCIM(
        currentLocation = commonViewModel.commonUiState.value.defaultLocationInfo,
        bitmap = bitmap,
        savePath = FileRepositoryImpl.DEFAULT_CAMERA_GPS_FOLDER_PATH,
        onSuccess = {
            launchMain {
                displayToast(getString(R.string.image_saved_format, it))
            }
        },
        onError = {
            launchMain {
                displayToast(getString(R.string.error_saving_image))
            }
        }
    )
}

fun CameraFragment.observePictureCountdownTimerTextVisibilityChanged() {
    viewModel.uiState
        .map { it.isPictureCountdownTimerCompletelyVisible() }
        .distinctUntilChanged()
        .onEach { isVisible ->
            binding.tvCountdownTimerPicture.isVisible = isVisible
        }
        .launchIn(viewLifecycleOwner.lifecycleScope)
}

fun CameraFragment.observeSavingStateChanged() {
    viewModel.uiState
        .map { it.isSaving }
        .distinctUntilChanged()
        .onEach { isSaving ->
            binding.btnCapture.isEnabled = !isSaving

            if (isSaving) {
                showHideLoading(true)
            } else {
                showHideLoading(false)
            }
        }
        .launchIn(viewLifecycleOwner.lifecycleScope)
}

fun CameraFragment.observeRecordingTimerVisibilityChanged() {
    viewModel.uiState
        .map { it.isRecordingTimerCompletelyVisible() }
        .distinctUntilChanged()
        .onEach { isVisible ->
            binding.tvRecordingTime.isVisible = isVisible
        }
        .launchIn(viewLifecycleOwner.lifecycleScope)
}

fun CameraFragment.observeToggleBetweenPictureAndVideoModeChanged() {
    viewModel.uiState
        .map { it.isShowingToggleBetweenPictureAndVideoModeButton() }
        .distinctUntilChanged()
        .onEach { isVisible ->
            binding.clToggleContainer.isVisible = isVisible
        }
        .launchIn(viewLifecycleOwner.lifecycleScope)
}

fun CameraFragment.observeTimeButtonVisibilityChanged() {
    viewModel.uiState
        .map { it.isTimeButtonCompletelyVisible() }
        .distinctUntilChanged()
        .onEach { isVisible ->
            binding.btnTime.visibility = if (isVisible) View.VISIBLE else View.GONE
        }
        .launchIn(viewLifecycleOwner.lifecycleScope)
}

fun CameraFragment.observeCurrentLocationItem() {
    commonViewModel.commonUiState
        .flatMapLatest {
            Timber.d("observeCurrentLocationItem: commonUiState: defaultLocationInfo: ${it.defaultLocationInfo}")
            Timber.d("observeCurrentLocationItem: commonUiState: defaultLayoutItem: ${it.defaultLayoutItem}")
            if (it.defaultLocationInfo != null && it.defaultLayoutItem != null) {
                flowOf(it)
            } else {
                flowOf(null)
            }
        }
        .onEach { commonUiState ->
            Timber.d("observeCurrentLocationItem: commonUiState: $commonUiState")
        }
        .flowWithLifecycle(lifecycle = lifecycle, minActiveState = Lifecycle.State.RESUMED)
        .filterNotNull()
        .onEach { commonUiState ->
            // Clear previous views
            val layout = commonUiState.defaultLayoutItem!!
            val locationInfo = commonUiState.defaultLocationInfo!!

            currentLocationView = null
            currentLocationView = LocationInfoLayoutFactory.createLocationInfoLayoutView(
                layout.layoutItem,
                requireContext()
            )
            currentLocationView?.removeSelf()
            binding.locationContainer.removeAllViews()
            binding.locationContainer.addView(currentLocationView)

            // Set data to the layout
            currentLocationView?.post {
                currentLocationView?.setLocationTitle(layout.itemData.defaultTitle)
                currentLocationView?.setLocationDisplayName(locationInfo.name)
                currentLocationView?.setLocationCoordinates("${locationInfo.latitude}, ${locationInfo.longitude}")
                currentLocationView?.setLocationTimestamp(TimeUtils.formatTimestamp(locationInfo.timestamp))
                currentLocationView?.setWeatherInfo(locationInfo.temperature, locationInfo.weatherCondition)

                currentLocationView?.setVisibilityTitle(layout.itemData.isTitleCompletelyVisible())
                currentLocationView?.setVisibilityDisplayName(layout.itemData.isLocationVisible)
                currentLocationView?.setVisibilityCoordinates(layout.itemData.isCoordinatesVisible)
                currentLocationView?.setVisibilityDateTime(layout.itemData.isDateTimeVisible)
                currentLocationView?.setVisibilityWeather(layout.itemData.isWeatherVisible)

                //Current location view is editable
                currentLocationView?.setOnClickEditButtonListener {
                    navigateToEditLocationGloballyMode()
                }

                // Update LocationView bitmap for OpenGL rendering
                updateLocationViewBitmap()
            }

        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun CameraFragment.observeEditButtonVisibilityChanged() {
    viewModel.uiState
        .map { it.isShowingEditLocationButton() }
        .distinctUntilChanged()
        .collectFlowOnView(viewLifecycleOwner, Lifecycle.State.RESUMED) { isVisible ->
            currentLocationView?.setVisibilityEditButton(isVisible)
        }
}

fun CameraFragment.navigateToEditLocationGloballyMode() {
    safeNav(
        R.id.cameraFragment,
        R.id.action_cameraFragment_to_editLocationNewFragment,
    )
}

fun CameraFragment.setUpCurrentLocation() {
    if (!isAllPermissionGranted(permissions)) {
        Timber.d("setupMap: isAllPermissionGranted: false")
        requestPermission(permissions) {}
        return
    }

    viewModel.getCurrentLocation(
        onSuccess = { locationInfo ->
            // Use Maps KTX to get current location
            Timber.d("setupMap: setCurrentLocation: $locationInfo")

            //Set default location info if not set yet
            if (commonViewModel.commonUiState.value.defaultLocationInfo == null) {
                commonViewModel.setDefaultLocationInfo(locationInfo)
            }
        },
        onError = { e ->
            displayToast(getString(R.string.error_getting_location, e?.message))
        }
    )
}

fun CameraFragment.loadCameraSettingsPreferences() {
    // Load grid mode
    val gridMode = prefUtil.defaultCameraGridMode
    viewModel.setGridMode(gridMode)

    // Load timer seconds
    val timerSeconds = prefUtil.defaultCameraPictureCountdownTimerSeconds
    viewModel.setTimerSeconds(timerSeconds)

    // Load aspect ratio
    val aspectRatioStr = prefUtil.defaultCameraAspectRatio
    val aspectRatio = AspectRatio.parse(aspectRatioStr)
    viewModel.setAspectRatio(aspectRatio)
}

fun CameraFragment.observeTimerCountdownSecondsChanged() {
    viewModel.uiState
        .map { it.cameraPictureCountdownUiState.timerSeconds }
        .distinctUntilChanged()
        .onEach { seconds ->
            binding.btnTime.setImageBitmap(timerIcons[seconds])
        }
        .launchIn(viewLifecycleOwner.lifecycleScope)
}

fun CameraFragment.observePauseResumeButtonVisibilityChanged() {
    viewModel.uiState
        .map { it.isShowingPauseResumeButton() }
        .distinctUntilChanged()
        .onEach { isVisible ->
            binding.btnPauseOrResumeVideoRecording.isVisible = isVisible
        }
        .launchIn(viewLifecycleOwner.lifecycleScope)
}

fun CameraFragment.observeSwitchFacingButtonVisibilityChanged() {
    viewModel.uiState
        .map { it.isShowingSwitchFacingButton() }
        .distinctUntilChanged()
        .onEach { isVisible ->
            binding.btnSwitchCamera.isVisible = isVisible
        }
        .launchIn(viewLifecycleOwner.lifecycleScope)
}

fun CameraFragment.observeGoToSaveMediaButtonVisibilityChanged() {
    viewModel.uiState
        .map { it.isShowingGoToSaveMediaButton() }
        .distinctUntilChanged()
        .onEach { isVisible ->
            binding.imgSavePhotoContent.isVisible = isVisible
        }
        .launchIn(viewLifecycleOwner.lifecycleScope)
}

fun CameraFragment.observeOrientationChanged() {
    lifecycleScope.launch {
        viewModel.uiState
            .map { it.cameraSettingUiState.deviceOrientation }
            .distinctUntilChanged()
            .collectLatest { orientation ->
                rotateLocationContainer(orientation)
            }
    }
}

fun CameraFragment.rotateLocationContainer(orientation: Int) {
    binding.locationContainer.apply {
        // Calculate rotation angle based on device orientation
        val rotationAngle = when (orientation) {
            0 -> 0f      // Portrait
            90 -> -90f   // Landscape left (rotate counter-clockwise)
            180 -> 180f  // Portrait upside down
            270 -> 90f   // Landscape right (rotate clockwise)
            else -> 0f
        }

        // Set pivot point to center for proper rotation
        post {
            pivotX = width / 2f
            pivotY = height / 2f

            // Apply rotation
            rotation = rotationAngle

            // Note: Container dimensions are managed by parent layout constraints
            // We focus on adjusting the location view positioning within the rotated container

            // Adjust gravity and margins to keep location view at bottom
            currentLocationView?.let { locationView ->
                val params = locationView.layoutParams as? FrameLayout.LayoutParams
                params?.let { layoutParams ->
                    when (orientation) {
                        0 -> {
                            Log.d("rotateLocationContainer", "Portrait")
                            // Portrait: bottom center
                            layoutParams.gravity = Gravity.BOTTOM or Gravity.CENTER_HORIZONTAL
                            layoutParams.setMargins(16, 0, 16, 32)
                        }
                        90 -> {
                            Log.d("rotateLocationContainer", "Landscape left")
                            // Landscape left: right center (after rotation becomes bottom)
                            layoutParams.gravity = Gravity.END or Gravity.CENTER_VERTICAL
                            layoutParams.setMargins(0, 16, 32, 50)
                        }
                        180 -> {
                            Log.d("rotateLocationContainer", "Portrait upside down")
                            // Portrait upside down: top center (after rotation becomes bottom)
                            layoutParams.gravity = Gravity.TOP or Gravity.CENTER_HORIZONTAL
                            layoutParams.setMargins(16, 32, 16, 32)
                        }
                        270 -> {
                            Log.d("rotateLocationContainer", "Landscape right")
                            // Landscape right: left center (after rotation becomes bottom)
                            layoutParams.gravity = Gravity.START or Gravity.CENTER_VERTICAL
                            layoutParams.setMargins(32, 16, 0, 50)
                        }
                    }
                    locationView.layoutParams = layoutParams
                }
            }

            // Request layout update
            requestLayout()
            invalidate()
        }
    }
}

fun CameraFragment.setupLocationViewRenderer() {
    // Initialize LocationView renderer for OpenGL rendering
    locationViewRenderer = LocationViewRenderer()

    // Add renderer to camera preview if it's a GL preview
    (cameraView as? GlCameraPreview)?.addRendererFrameCallback(locationViewRenderer!!)

    Log.d(TAG, "LocationViewRenderer initialized and added to camera preview")
}

fun CameraFragment.observeLocationViewRenderingState() {
    lifecycleScope.launch {
        viewModel.uiState
            .map { it.locationViewRenderingUiState }
            .distinctUntilChanged()
            .collectLatest { renderingState ->
                locationViewRenderer?.apply {
                    setVisible(renderingState.isLocationViewVisible)
                    setRenderingPosition(renderingState.renderingPosition)
                    updateLocationViewBitmap(renderingState.locationViewBitmap)
                }
            }
    }
}

fun CameraFragment.updateLocationViewBitmap() {
    currentLocationView?.let { locationView ->
        // Create bitmap from current location view
        val bitmap = locationView.createBitmapFromView()

        // Update the UI state with the new bitmap
        viewModel.setLocationViewBitmap(bitmap)

        Log.d(TAG, "Updated LocationView bitmap: ${bitmap.width}x${bitmap.height}")
    }
}
